# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/PagesChanged.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import AppPage_pb2 as streamlit_dot_proto_dot_AppPage__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"streamlit/proto/PagesChanged.proto\x1a\x1dstreamlit/proto/AppPage.proto\"+\n\x0cPagesChanged\x12\x1b\n\tapp_pages\x18\x01 \x03(\x0b\x32\x08.AppPageB1\n\x1c\x63om.snowflake.apps.streamlitB\x11PagesChangedProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.PagesChanged_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\021PagesChangedProto'
  _globals['_PAGESCHANGED']._serialized_start=69
  _globals['_PAGESCHANGED']._serialized_end=112
# @@protoc_insertion_point(module_scope)

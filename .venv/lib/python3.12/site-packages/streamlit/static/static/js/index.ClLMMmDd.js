import{r as f,z as T,B as C,j as n,C as y,bp as S,aC as L,bx as W,bq as B,b7 as w,bH as R,J as E,bI as $,bJ as P}from"./index.BYo0ywlm.js";import{a as v}from"./useBasicWidgetState.Cwd7-jJa.js";import{a as H,L as X,S as p}from"./checkbox.BUm2vnNv.js";import"./FormClearHelper.CsFEiTNN.js";function M({element:e,disabled:a,widgetMgr:s,fragmentId:d}){var m;const[x,g]=v({getStateFromWidgetMgr:V,getDefaultStateFromProto:I,getCurrStateFromProto:j,updateWidgetMgrState:z,element:e,widgetMgr:s,fragmentId:d}),k=f.useCallback(i=>{g({value:i.target.checked,fromUi:!0})},[g]),t=T(),{colors:o,spacing:h,sizes:r}=t,b=C(t),u=a?o.fadedText40:o.bodyText;return n(P,{className:"row-widget stCheckbox","data-testid":"stCheckbox",children:n(H,{checked:x,disabled:a,onChange:k,"aria-label":e.label,checkmarkType:e.type===$.StyleType.TOGGLE?p.toggle:p.default,labelPlacement:X.right,overrides:{Root:{style:({$isFocusVisible:i})=>({marginBottom:h.none,marginTop:h.none,backgroundColor:i?o.darkenedBgMix25:"",display:"flex",alignItems:"start"})},Toggle:{style:({$checked:i})=>{let c=b?o.bgColor:o.bodyText;return a&&(c=b?o.gray70:o.gray90),{width:`calc(${r.checkbox} - ${t.spacing.twoXS})`,height:`calc(${r.checkbox} - ${t.spacing.twoXS})`,transform:i?`translateX(${r.checkbox})`:"",backgroundColor:c,boxShadow:""}}},ToggleTrack:{style:({$checked:i,$isHovered:c})=>{let l=o.fadedText40;return c&&!a&&(l=o.fadedText20),i&&!a&&(l=o.primary),{marginRight:0,marginLeft:0,marginBottom:0,marginTop:t.spacing.twoXS,paddingLeft:t.spacing.threeXS,paddingRight:t.spacing.threeXS,width:`calc(2 * ${r.checkbox})`,minWidth:`calc(2 * ${r.checkbox})`,height:r.checkbox,minHeight:r.checkbox,borderBottomLeftRadius:t.radii.full,borderTopLeftRadius:t.radii.full,borderBottomRightRadius:t.radii.full,borderTopRightRadius:t.radii.full,backgroundColor:l}}},Checkmark:{style:({$isFocusVisible:i,$checked:c})=>{const l=c&&!a?o.primary:o.fadedText40;return{outline:0,width:r.checkbox,height:r.checkbox,marginTop:t.spacing.twoXS,marginLeft:0,marginBottom:0,boxShadow:i&&c?`0 0 0 0.2rem ${E(o.primary,.5)}`:"",borderLeftWidth:r.borderWidth,borderRightWidth:r.borderWidth,borderTopWidth:r.borderWidth,borderBottomWidth:r.borderWidth,borderLeftColor:l,borderRightColor:l,borderTopColor:l,borderBottomColor:l}}},Label:{style:{lineHeight:t.lineHeights.small,paddingLeft:t.spacing.sm,position:"relative",color:u}}},children:y(R,{visibility:S((m=e.labelVisibility)==null?void 0:m.value),"data-testid":"stWidgetLabel",children:[n(L,{source:e.label,allowHTML:!1,isLabel:!0,largerLabel:!0}),e.help&&n(W,{color:u,children:n(B,{content:e.help,placement:w.TOP_RIGHT})})]})})})}function V(e,a){return e.getBoolValue(a)}function I(e){return e.default??null}function j(e){return e.value??null}function z(e,a,s,d){a.setBoolValue(e,s.value,{fromUi:s.fromUi},d)}const A=f.memo(M);export{A as default};

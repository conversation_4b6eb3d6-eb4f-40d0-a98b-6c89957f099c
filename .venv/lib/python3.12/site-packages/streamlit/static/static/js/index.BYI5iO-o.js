import{n as c,r as s,aE as f,b6 as b,z as x,j as a,b3 as k,b7 as C,C as L,D as v,aC as h}from"./index.BYo0ywlm.js";const T=c("div",{target:"e1ghu24d0"})(({containerWidth:n})=>({display:"flex",flexDirection:"column",width:n?"100%":"fit-content"})),w=c("a",{target:"e1ghu24d1"})(({disabled:n,isCurrentPage:e,containerWidth:i,theme:o})=>({textDecoration:"none",width:i?"100%":"fit-content",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"flex-start",gap:o.spacing.sm,borderRadius:o.radii.button,paddingLeft:o.spacing.sm,paddingRight:o.spacing.sm,marginTop:o.spacing.threeXS,marginBottom:o.spacing.threeXS,lineHeight:o.lineHeights.menuItem,backgroundColor:e?o.colors.darkenedBgMix15:"transparent","&:hover":{backgroundColor:e?o.colors.darkenedBgMix25:o.colors.darkenedBgMix15},"&:active,&:visited,&:hover":{textDecoration:"none"},"&:focus":{outline:"none"},"&:focus-visible":{backgroundColor:o.colors.darkenedBgMix15},"@media print":{paddingLeft:o.spacing.none},...n?{borderColor:o.colors.borderColor,backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed","&:hover":{color:o.colors.fadedText40,backgroundColor:o.colors.transparent}}:{}})),S=c("span",{target:"e1ghu24d2"})(({disabled:n,theme:e})=>({color:e.colors.bodyText,overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",display:"table-cell",...n?{borderColor:e.colors.borderColor,backgroundColor:e.colors.transparent,color:e.colors.fadedText40,cursor:"not-allowed"}:{}}));function y(n,e){return n===null&&e?!0:n===null&&!e?!1:n===!0}function P(n){const{onPageChange:e,currentPageScriptHash:i}=s.useContext(f),o=s.useContext(b),{colors:d}=x(),{disabled:t,element:r}=n,l=y(r.useContainerWidth,o),g=i===r.pageScriptHash,p=u=>{r.external?t&&u.preventDefault():(u.preventDefault(),t||e(r.pageScriptHash))};return a("div",{className:"stPageLink","data-testid":"stPageLink",children:a(k,{help:r.help,placement:C.TOP_RIGHT,containerWidth:l,children:a(T,{containerWidth:l,children:L(w,{"data-testid":"stPageLink-NavLink",disabled:t,isCurrentPage:g,containerWidth:l,href:r.page,target:r.external?"_blank":"",rel:"noreferrer",onClick:p,children:[r.icon&&a(v,{size:"lg",color:t?d.fadedText40:d.bodyText,iconValue:r.icon}),a(S,{disabled:t,children:a(h,{source:r.label,allowHTML:!1,isLabel:!0,boldLabel:g,largerLabel:!0,disableLinks:!0})})]})})})})}const B=s.memo(P);export{B as default};

#!/bin/bash

# PDF Auto-Cropper Batch Script
# Processes all PDF files in the Downloads folder and crops them in place
# Uses the crop.py command line tool with auto-detection

set -e  # Exit on any error

# Configuration
DOWNLOADS_DIR="$HOME/Downloads"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CROP_SCRIPT="$SCRIPT_DIR/crop.py"

# Source environment
source "$SCRIPT_DIR/.venv/bin/activate"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Python is available
    if ! command_exists python3; then
        print_error "Python 3 is required but not found. Please install Python 3."
        exit 1
    fi
    
    # Check if crop.py exists
    if [[ ! -f "$CROP_SCRIPT" ]]; then
        print_error "crop.py not found at $CROP_SCRIPT"
        print_error "Please ensure crop.py is in the same directory as this script."
        exit 1
    fi
    
    # Check if crop.py is executable
    if [[ ! -x "$CROP_SCRIPT" ]]; then
        print_warning "Making crop.py executable..."
        chmod +x "$CROP_SCRIPT"
    fi
    
    # Check if Downloads directory exists
    if [[ ! -d "$DOWNLOADS_DIR" ]]; then
        print_error "Downloads directory not found: $DOWNLOADS_DIR"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to backup a file
backup_file() {
    local file="$1"
    local backup="${file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    if cp "$file" "$backup"; then
        print_status "Created backup: $(basename "$backup")"
        echo "$backup"
    else
        print_error "Failed to create backup for $(basename "$file")"
        return 1
    fi
}

# Function to crop a single PDF
crop_pdf_file() {
    local pdf_file="$1"
    local filename=$(basename "$pdf_file")
    local temp_output="${pdf_file}.tmp_cropped"
    local backup_file=""
    local success=0

    print_status "Processing: $filename"

    # Create backup
    if ! backup_file="$(backup_file "$pdf_file")"; then
        return 1
    fi

    # Run crop.py with auto-detection
    if python3 "$CROP_SCRIPT" "$pdf_file" --output "$temp_output"; then
        # Replace original file with cropped version
        if mv "$temp_output" "$pdf_file"; then
            print_success "Cropped: $filename"
            success=1
        else
            print_error "Failed to replace original file: $filename"
            # Restore from backup
            mv "$backup_file" "$pdf_file"
            rm -f "$temp_output"
        fi
    else
        print_error "Failed to crop: $filename"
        # Restore from backup if cropping failed
        if [[ -f "$backup_file" ]]; then
            mv "$backup_file" "$pdf_file"
        fi
        rm -f "$temp_output"
    fi

    # Always clean up backup file at the end
    if [[ -f "$backup_file" ]]; then
        rm -f "$backup_file"
    fi

    return $success
}

# Main function
main() {
    echo "=================================================="
    echo "PDF Auto-Cropper Batch Processing Script"
    echo "=================================================="
    echo
    
    check_prerequisites
    echo
    
    print_status "Scanning Downloads folder: $DOWNLOADS_DIR"
    
    # Find all PDF files in Downloads
    pdf_files=()
    while IFS= read -r -d '' file; do
        pdf_files+=("$file")
    done < <(find "$DOWNLOADS_DIR" -maxdepth 1 -name "*.pdf" -type f -print0 2>/dev/null)
    
    if [[ ${#pdf_files[@]} -eq 0 ]]; then
        print_warning "No PDF files found in $DOWNLOADS_DIR"
        exit 0
    fi
    
    echo
    print_status "Found ${#pdf_files[@]} PDF file(s) to process:"
    for pdf in "${pdf_files[@]}"; do
        echo "  - $(basename "$pdf")"
    done
    echo
    
    # Ask for confirmation
    # read -p "Do you want to proceed with cropping these files? (y/N): " -n 1 -r
    # echo
    # if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    #     print_status "Operation cancelled by user."
    #     exit 0
    # fi
    
    echo
    print_status "Starting batch processing..."
    echo
    
    # Process each PDF
    local success_count=0
    local error_count=0
    
    for pdf_file in "${pdf_files[@]}"; do
        if crop_pdf_file "$pdf_file"; then
            ((success_count++))
        else
            ((error_count++))
        fi
        echo
    done
    
    # Summary
    echo "=================================================="
    echo "Batch Processing Complete"
    echo "=================================================="
    print_success "Successfully processed: $success_count files"
    if [[ $error_count -gt 0 ]]; then
        print_error "Failed to process: $error_count files"
    fi
    echo
    
    if [[ $error_count -eq 0 ]]; then
        print_success "All PDF files have been cropped successfully!"
    else
        print_warning "Some files could not be processed. Check the error messages above."
        exit 1
    fi
}

# Handle script interruption
trap 'echo; print_warning "Script interrupted by user."; exit 130' INT

# Run main function
main "$@"

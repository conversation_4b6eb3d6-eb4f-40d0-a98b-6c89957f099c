# PDF Auto-Cropper for E-ink Devices

A Python tool that automatically crops PDF pages to remove margins and creates uniform-sized pages, optimized for better viewing on e-ink devices like Kindle, Kobo, and other e-readers.

# Installation

## Install uv

Run `curl -LsSf https://astral.sh/uv/install.sh | sh` on your terminal. Close and re open the window.

## Install

From the `crop` directory, run `./install.sh`. This will install everything.

## Run

Run `./run.sh` from the `crop` directory. This will start running and show a URL. Leave the terminal
open and navigate to `http://localhost:8501` (or whatever address shows up). Then drag and drop in your
PDF and convert.